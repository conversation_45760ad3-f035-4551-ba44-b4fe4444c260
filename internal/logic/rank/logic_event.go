package logic_rank

import (
	"context"
	"ranksrv/internal/dao/dao_rank_player"
	"ranksrv/internal/dao/dao_schedule"
	"ranksrv/internal/dao/dao_sort"
	"ranksrv/internal/logic/dispose"
	"ranksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

// 更新
func UpdateRankPlayer(ctx context.Context, event *commonPB.EventCommon) {
	schList, err := dao_schedule.QueryAllSchdule(ctx)
	if err != nil {
		//
		logrus.Errorf("get all sch err:%+v", err)
		return
	}

	for _, sch := range schList {
		ds := dispose.GetDispose(sch.RankType)
		if ds == nil {
			logrus.Errorf("unknow dispose:%+v", sch.RankType)
			continue
		}

		// 不符合条件
		if !ds.Event(event.EventType) {
			continue
		}
		// TODO: 用户分布式锁
		players, err := dao_rank_player.QueryRankPlayers(ctx, sch.RankId, sch.Round, transform.Uint642Str(event.PlayerId))
		if err != nil {
			logrus.Errorf("update rank player error:%+v event:%+v sch:%+v", err, event, sch)
			return
		}
		// 更新玩家数据
		var player *model.TRankPlayer
		if len(players) == 1 {
			player = players[0]
		} else {
			player = model.NewPlayer(event.PlayerId)

		}
		br := ds.UpdateRankPlayer(ctx, event, player)
		_ = br
		// 数据有没有更新
		if !br {
			continue
		}

		// 保存玩家数据
		err = dao_rank_player.UpdatePlayerInfo(ctx, sch, player)
		if err != nil {
			logrus.Errorf("update rank player error:%+v event:%+v sch:%+v", err, event, sch)
			return
		}
		score := ds.CalcWeight(ctx, player)

		// 更新排名数据
		err = dao_sort.UpdateRankSort(ctx, sch, event.PlayerId, score)
		if err != nil {
			logrus.Errorf("update rank player error:%+v event:%+v sch:%+v", err, event, sch)
			return
		}
		// XXX: 广播推送
	}
}
