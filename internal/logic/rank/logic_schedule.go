package logic_rank

import (
	"context"
	"errors"
	"fmt"
	"ranksrv/config"
	"ranksrv/internal/dao/dao_rank_player"
	"ranksrv/internal/dao/dao_schedule"
	"ranksrv/internal/model"
	"ranksrv/internal/repo/rpc_msg"
	"sort"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// GetRankSchedule 获取排行榜排期数据
func GetRankSchedule(ctx context.Context, rankId int64) (*model.TRankSchedule, error) {
	// 查询排期
	sch, err := dao_schedule.QuerySchedule(ctx, rankId)
	if err != nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, fmt.Sprintf("rank:%+v fail:%+v", rankId, err))
	}
	if sch.Status == config.RANK_STATUS_CLOSE {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_RANK_NOT_OPEN, fmt.Sprintf("rank:%+v not open", rankId))
	}
	return sch, nil
}

// 检查排期状态
func CheckSchedule() {
	logrus.Debug("do check schedule")
	// 在上层做分布式任务

	// 尝试查询所有排期(配置表)
	ctx := context.TODO()

	// XXX: 排行榜配置删除怎么办
	allRankCfg := cmodel.GetAllRank()
	for _, rankCfg := range allRankCfg {
		doCheckSchedule(ctx, rankCfg)
	}
}

func doCheckSchedule(ctx context.Context, rankCfg *cmodel.Rank) {
	// 构建排期对象

	// 当前版本不对源对象做任何改变
	// 等产品出方案
	sch, err := dao_schedule.QuerySchedule(ctx, rankCfg.Id)
	// 排期不存在
	if err != nil {
		// 不是空情况
		if !errors.Is(err, redis.Nil) {
			logrus.Errorf("cache.FindSchedule[%+v] error:%+v", rankCfg.Id, err)
			return
		}
		// 初始化排期
		InitSch(ctx, rankCfg, 0)
		return
	}
	// XXX 刷新排期数据

	// 是否下一周期
	ts := timex.Now().Unix()
	if sch.Status != config.RANK_STATUS_OPEN {
		return
	}
	// 触发发奖流程
	if sch.RewardStatus == config.RANK_REWARD_STATUS_NONE && ts >= sch.RewardTime {
		// TODO：进入发奖周期不再更新记录数据
		// Todo 转存清单
		RewardSch(ctx, sch, false)
		sch.RewardStatus = config.RANK_REWARD_STATUS_REWARDED
		dao_schedule.SaveSchedule(ctx, sch)
	}
	if ts >= sch.FlushTime && sch.FlushTime != 0 {
		InitSch(ctx, rankCfg, sch.Round+1)
		return
	} else if sch.FlushTime == 0 && sch.RewardStatus == config.RANK_REWARD_STATUS_REWARDED {
		// 续租
		InitSch(ctx, rankCfg, sch.Round)
		return
	}
	// XXX: 是否对 sch刷新
}

// InitSch 初始化排期
func InitSch(ctx context.Context, rankCfg *cmodel.Rank, round int32) {
	// 初始化排期
	new := model.NewSchFromConfig(rankCfg)
	new.Round = round
	err := dao_schedule.SaveSchedule(ctx, new)
	if err != nil {
		logrus.Errorf("cache.Savechedule[%+v] error:%+v", rankCfg.Id, err)
		return
	}
	// 缓存列表
	logrus.Infof("init schedule:%+v", new)
}

// RewardSch 发奖
func RewardSch(ctx context.Context, sch *model.TRankSchedule, isGm bool) {
	logrus.Infof("reward sch:%+v begin", sch)
	pList, err := GetRankSort(ctx, sch.RankId, sch.Round, 0, config.DEFAULT_RANK_SORT_SIZE)
	if err != nil {
		logrus.Errorf("get sort fail %+v", err)
		return
	}
	playerIds := make([]uint64, 0)
	for _, p := range pList {
		if p.RewardStatus == config.RANK_REWARD_STATUS_NONE {
			playerIds = append(playerIds, p.PlayerId)
		}
	}
	userList, err := crpc_user.RpcQueryPlayerBriefMulti(ctx, playerIds)
	if err != nil {
		logrus.Warnf("get player info failed:%+v", err)
		return
	}

	rankCfg := cmodel.GetRank(sch.RankId)
	awardCfg := MakeReward(sch)

	// 开始发奖
	for _, p := range pList {
		if p.RewardStatus == config.RANK_REWARD_STATUS_NONE || isGm || (p.RewardStatus == config.RANK_REWARD_STATUS_REWARDED && p.RewardTs+3600 < timex.Now().Unix()) {
			mail := MakeMail(ctx, rankCfg, p, userList[p.PlayerId], awardCfg)
			// 更新状态
			p.RewardStatus = config.RANK_REWARD_STATUS_REWARDED
			p.RewardTs = timex.Now().Unix()
			err := dao_rank_player.UpdatePlayerInfo(ctx, sch, p)
			if err != nil {
				logrus.Errorf("update reward player fail:%+v", err)
				continue
			}

			err = rpc_msg.SendMail(ctx, p.PlayerId, mail)
			if err != nil {
				logrus.Errorf("mail reward player fail:%+v playerId:%+v mail:%+v", err, p.PlayerId, mail)
				continue
			}
			logrus.Infof("reward player:%+v mail:%+v", p.PlayerId, mail)
		}
	}
	logrus.Infof("reward sch:%+v end", sch)
}

func MakeMail(ctx context.Context, rankCfg *cmodel.Rank, rp *model.TRankPlayer, userInfo *commonPB.BriefUserInfo, rewardCfg []*rankRewardCfg) *commonPB.MailAssembly {
	attach := FindReward(rp.Sort, rewardCfg)
	if attach == nil {
		logrus.Warnf("no reward cfg for player:%+v, sort:%+v", rp.PlayerId, rp.Sort)
		// 没有奖励配置
		return nil
	}
	if attach.TemplateId == 0 {
		logrus.Warnf("no template id for player:%+v, sort:%+v", rp.PlayerId, rp.Sort)
		return nil
	}

	extend := make(map[int32]int64, 0)
	extend[int32(commonPB.MAIL_EXTEND_KEY_MEK_RANK_ID)] = rankCfg.Id
	extend[int32(commonPB.MAIL_EXTEND_KEY_MEK_RANK_SORT)] = int64(rp.Sort)
	mail := &commonPB.MailAssembly{
		TemplateId:  attach.TemplateId,
		Extend:      extend,
		MailType:    commonPB.MAIL_TYPE_MT_ORDINARY,
		CreateTime:  timex.Now().Unix(),
		ExpiresTime: timex.Now().Unix() + 7*86400,
		Rewards:     attach.reward,
	}
	return mail
}

type rankRewardCfg struct {
	Sort       int32
	reward     *commonPB.ItemBaseList
	TemplateId int64
}

func FindReward(sort int32, cfgs []*rankRewardCfg) *rankRewardCfg {
	if len(cfgs) == 0 {
		return nil
	}
	for _, cfg := range cfgs {
		if sort <= cfg.Sort {
			return cfg
		}
	}
	// 超出没奖励
	return nil
}

func MakeReward(sch *model.TRankSchedule) []*rankRewardCfg {
	list := make([]*rankRewardCfg, 0)
	rankAwardCfg := cmodel.GetAllRankReward()

	for _, cfg := range rankAwardCfg {
		if sch.RankId == cfg.RankId {
			reward := &commonPB.ItemBaseList{}
			for _, item := range cfg.Award {
				reward.ItemList = append(reward.ItemList,
					&commonPB.ItemBase{ItemId: item.ItemId, ItemCount: item.Val})
			}
			list = append(list, &rankRewardCfg{
				Sort:       cfg.Rank,
				reward:     reward,
				TemplateId: cfg.TemplateId,
			})
		}
	}
	sort.Slice(list, func(i, j int) bool {
		return list[i].Sort < list[j].Sort
	})

	return list
}
