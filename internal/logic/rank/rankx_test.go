package logic_rank

import (
	"context"
	"fmt"
	"ranksrv/internal/model"
	"ranksrv/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
)

func init() {
	test.Init()
}

func TestGetRange(t *testing.T) {
	test.Init()
	ctx := context.TODO()
	data, err := GetRankSchedule(ctx, 1)
	if err != nil {
		t.Errorf("err:%+v", err)
		return
	}
	logrus.Infof("data:%+v", data)
}

func TestCheckSch(t *testing.T) {
	CheckSchedule()
}

func TestEvent(t *testing.T) {
	test.Init()
	ctx := test.TestCtx(1)
	ev := &commonPB.EventCommon{
		PlayerId:  40,
		ProductId: 1,
		ChannelId: 1001,
		EventType: commonPB.EVENT_TYPE_ET_TRIP_SETTLE_VAL,
		IntData: map[int32]int64{
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_POND):     301020000,
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_BAIT):     305107001,
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_ID):       101031009,
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT):   random.Int64n(3000),
			int32(commonPB.EVENT_INT_KEY_EIK_TRIP_FISH_VAL): random.Int64n(5000),
		},
	}
	UpdateRankPlayer(ctx, ev)
}

func TestReward(t *testing.T) {
	test.Init()
	// ctx := test.TestCtx(1)
	sch := &model.TRankSchedule{
		RankId: 201510001,
	}
	rankRewardCfgs := MakeReward(sch)
	reward := FindReward(9999, rankRewardCfgs)
	fmt.Println(reward)
}

func TestSrvReward(t *testing.T) {

	test.Init()
	ctx := test.TestCtx(1)
	sch := &model.TRankSchedule{
		RankId: 201510002,
	}
	RewardSch(ctx, sch, true)

}