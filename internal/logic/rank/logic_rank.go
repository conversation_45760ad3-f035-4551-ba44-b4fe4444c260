package logic_rank

// 封装接口用

import (
	"context"
	"ranksrv/config"
	"ranksrv/internal/dao/dao_rank_player"
	"ranksrv/internal/dao/dao_sort"
	"ranksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

// GetRankSort 获取排行榜数据
func GetRankSort(ctx context.Context, rankId int64, round int32, start, end int32) ([]*model.TRankPlayer, error) {
	var err error
	if start <= 0 {
		start = 0
	}
	if end <= 0 || end > config.DEFAULT_RANK_SORT_SIZE {
		end = config.DEFAULT_RANK_SORT_SIZE
	}
	if start > end {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "rank range start > end")
	}

	playerIds, err := dao_sort.FindRankSortRange(ctx, rankId, round, start, end)
	if err != nil {
		return nil, err
	}
	if len(playerIds) == 0 {
		return []*model.TRankPlayer{}, nil
	}

	players, err := dao_rank_player.QueryRankPlayers(ctx, rankId, round, playerIds...)
	if err != nil {
		return nil, err
	}

	// 重新赋值排名
	for i, p := range players {
		p.Sort = start + int32(i) + 1 // +1 是为了从1开始算
	}

	return players, nil
}

func MakePBRankPlayer(ctx context.Context, pList ...*model.TRankPlayer) []*commonPB.RankPlayer {
	opt := interceptor.GetRPCOptions(ctx)
	productId := opt.ProductId

	list := make([]*commonPB.RankPlayer, 0)

	playerIds := make([]uint64, 0)
	for _, p := range pList {
		playerIds = append(playerIds, p.PlayerId)
	}
	userList, err := crpc_user.RpcQueryPlayerInfoMulti(ctx, productId, playerIds)
	if err != nil {
		logrus.Warnf("get player info failed:%+v", err)
		userList = make(map[uint64]*commonPB.RichUserInfo)
	}

	for _, p := range pList {
		info := make(map[int32]int64)
		for k, v := range p.Data {
			info[int32(k)] = v
		}
		rp := &commonPB.RankPlayer{
			Rank: p.Sort,
			Info: info,
		}
		if user, ok := userList[p.PlayerId]; ok {
			rp.User = user.BriefUserInfo
		} else {
			rp.User = &commonPB.BriefUserInfo{
				PlayerId: p.PlayerId,
			}
		}
		list = append(list, rp)
	}
	return list
}

// GetPlayerRank 查询玩家排名信息
func GetPlayerRank(ctx context.Context, rankId int64, round int32, playerId uint64) (*model.TRankPlayer, error) {
	var err error
	//
	players, err := dao_rank_player.QueryRankPlayers(ctx, rankId, round, transform.Uint642Str(playerId))
	if err != nil {
		return nil, err
	}
	// 无用户数据j
	if len(players) == 0 {
		return model.NewPlayer(playerId), nil
	}
	p := players[0]
	// 找对应排名
	sort, err := dao_sort.FindRankSortPlayer(ctx, rankId, round, playerId)
	if err != nil {
		return nil, err
	}
	p.Sort = int32(sort) + 1 // 排名从 1开始
	if p.Sort > config.DEFAULT_RANK_SORT_SIZE {
		p.Sort = config.DEFAULT_RANK_UNRANK
	}

	return p, nil
}
