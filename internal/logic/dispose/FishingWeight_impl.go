package dispose

import (
	"context"
	"ranksrv/internal/model"
	"ranksrv/utility"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// 钓鱼价值榜
type FishingWeightImpl struct {
}

// 适用的事件类型
func (impl *FishingWeightImpl) Event(ev commonPB.EVENT_TYPE) bool {
	switch ev {
	case commonPB.EVENT_TYPE_ET_TRIP_SETTLE_WEIGHT:
		return true
	default:
		return false
	}
}

// 更新用户数据
func (impl *FishingWeightImpl) UpdateRankPlayer(ctx context.Context, ev *commonPB.EventCommon, player *model.TRankPlayer) bool {

	// 提取更新数据
	newWeight := ev.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT)]
	oldWeight := player.Data[commonPB.RANK_INFO_KEY_RIK_FISH_WEIGHT]

	if newWeight <= oldWeight {
		return false
	}

	player.Data[commonPB.RANK_INFO_KEY_RIK_FISH_POND] = ev.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_POND)]
	player.Data[(commonPB.RANK_INFO_KEY_RIK_BAIT_ID)] = ev.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_BAIT)]
	player.Data[(commonPB.RANK_INFO_KEY_RIK_FISH_ID)] = ev.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_ID)]
	player.Data[(commonPB.RANK_INFO_KEY_RIK_FISH_LENGTH)] = ev.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_LENGTH)]
	player.Data[commonPB.RANK_INFO_KEY_RIK_FISH_WEIGHT] = newWeight
	player.Data[commonPB.RANK_INFO_KEY_RIK_FISH_VALUE] = ev.IntData[int32(commonPB.EVENT_INT_KEY_EIK_TRIP_FISH_VAL)]
	player.Data[(commonPB.RANK_INFO_KEY_RIK_FISH_BROKEN)] = ev.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_BROKEN)]
	player.UpdateTs = timex.Now().Unix()

	return true
}

// 计算评分权重
func (impl *FishingWeightImpl) CalcWeight(ctx context.Context, player *model.TRankPlayer) int64 {
	// score = 双精度-+63
	// 假如val = int32
	// val << 31 +
	val := player.Data[commonPB.RANK_INFO_KEY_RIK_FISH_WEIGHT]

	ts := player.UpdateTs
	unTs := utility.TimeVal(ts)

	return val<<31 + unTs
}
