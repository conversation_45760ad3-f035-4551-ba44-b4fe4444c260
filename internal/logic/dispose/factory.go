package dispose

import (
	"context"
	"ranksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

type RankDispose interface {
	Event(ev commonPB.EVENT_TYPE) bool
	UpdateRankPlayer(ctx context.Context, ev *commonPB.EventCommon, player *model.TRankPlayer) bool
	CalcWeight(ctx context.Context, player *model.TRankPlayer) int64
}

func GetDispose(typ commonPB.RANK_TYPE) RankDispose {
	switch typ {
	case commonPB.RANK_TYPE_RT_FISHING_VALUE:
		return &FishingValImpl{}
	case commonPB.RANK_TYPE_RT_FISHING_WEIGHT:
		return &FishingWeightImpl{}
	default:
		return nil
	}
}
