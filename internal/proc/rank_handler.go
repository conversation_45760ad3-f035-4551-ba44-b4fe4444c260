package proc

import (
	"context"
	"ranksrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	rankPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/rank" //
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranet_grpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)
func regRankHandler() {
	handler := GetRankHandler()

	// 获取排行榜
	transport.Handler(int(commonPB.MsgID_CMD_RANK_GET_RANK_LIST_REQ), handler.GetRankListReq)
}

type RankHandler struct{}

func GetRankHandler() *RankHandler {
	return &RankHandler{}
}

func (h *RankHandler) GetRankListReq(ctx context.Context, _ *intranet_grpc.Header, req *rankPB.GetRankListReq) *transport.ResponseMsg {
	rsp := services.GetRankServiceInstance().GetRankListReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_RANK_GET_RANK_LIST_RSP, rsp)
}
