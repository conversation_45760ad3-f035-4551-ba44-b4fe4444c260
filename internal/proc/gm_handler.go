package proc

import (
	"ranksrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/gm_handler"
)

func regGmHandler() {
	gm_handler.InitGmHandler()
	instance := services.GetRankGmServiceInstance()
	gm_handler.Handler(commonPB.GM_CMD_GC_RANK_FLUSH_RANK, instance.FlushRank)
	gm_handler.Handler(commonPB.GM_CMD_GC_RANK_REWARD_RANK, instance.RewardRank)
}
