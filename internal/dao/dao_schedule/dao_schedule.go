package dao_schedule

import (
	"context"
	"errors"
	"ranksrv/config"
	"ranksrv/internal/model"
	"ranksrv/utility"
	"strconv"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// QuerySchedule 查找排位赛信息
func QuerySchedule(ctx context.Context, rankId int64) (*model.TRankSchedule, error) {
	key := config.GetRedisKeyRankSchdule(rankId)

	data, err := redisx.GetGeneralCli().Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	m := &model.TRankSchedule{}
	err = m.Deserialize([]byte(data))
	if err != nil {
		return nil, err
	}
	return m, nil
}

// SaveSchdule 保存排位赛信息
func SaveSchedule(ctx context.Context, m *model.TRankSchedule) error {
	key := config.GetRedisKeyRankSchdule(m.RankId)

	// 更新列表状态
	listKey := config.GetRedisKeyRankSchduleList()
	endTs := m.GetExpire()

	pipe := redisx.GetGeneralCli().Pipeline()
	pipe.Set(ctx, key, m.Serialize(), 0)
	pipe.HSet(ctx, listKey, m.RankId, 1)
	pipe.ExpireAt(ctx, key, utility.RandTimeExpire(endTs))
	// 刷新过期时间
	playerKey := config.GetRedisKeyRankPlayer(m.RankId, m.Round)
	pipe.ExpireAt(ctx, playerKey, utility.RandTimeExpire(endTs))
	sortKey := config.GetRedisKeyRankSort(m.RankId, m.Round)
	pipe.ExpireAt(ctx, sortKey, utility.RandTimeExpire(endTs))

	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}

	return nil
}

func QueryAllSchdule(ctx context.Context) ([]*model.TRankSchedule, error) {
	// TODO: 本地缓存器
	key := config.GetRedisKeyRankSchduleList()

	kvs, err := redisx.GetGeneralCli().HGetAll(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return []*model.TRankSchedule{}, nil
		}
	}
	list := make([]*model.TRankSchedule, 0)
	for key, _ := range kvs {
		rankId, _ := strconv.Atoi(key)
		sch, err := QuerySchedule(ctx, int64(rankId))
		if err != nil {
			if errors.Is(err, redis.Nil) {
				continue
			}
			return nil, err
		}
		list = append(list, sch)
	}
	return list, nil
}
