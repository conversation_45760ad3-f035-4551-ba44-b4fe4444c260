package dao_rank_player

import (
	"ranksrv/internal/model"
	"ranksrv/test"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/sirupsen/logrus"
)

func TestGet(t *testing.T) {
	test.Init()

	ctx := test.TestCtx(1)

	l, err := QueryRankPlayers(ctx, 1, 1, "1", "2")

	if err != nil {
		t.Fatalf("err:%+v", err)
	}
	logrus.Debugf("info:%+v", l)
}

func TestSet(t *testing.T) {
	test.Init()
	sch := &model.TRankSchedule{
		RankId:    1,
		Round:     1,
		FlushTime: 0,
	}

	ctx := test.TestCtx(1)

	if err := UpdatePlayerInfo(ctx, sch, &model.TRankPlayer{
		PlayerId: 1,
		Data: map[commonPB.RANK_INFO_KEY]int64{
			commonPB.RANK_INFO_KEY_RIK_BAIT_ID: 123,
		},
	}); err != nil {
		t.Fatalf("err:%+v", err)
	}

}
