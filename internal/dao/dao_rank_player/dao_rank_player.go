package dao_rank_player

import (
	"context"
	"fmt"
	"ranksrv/config"
	"ranksrv/internal/model"
	"ranksrv/utility"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// QueryRankPlayers 查询指定排行榜玩家
func QueryRankPlayers(ctx context.Context, rankId int64, round int32, playerIds ...string) ([]*model.TRankPlayer, error) {
	key := config.GetRedisKeyRankPlayer(rankId, round)
	beans, err := redisx.GetGeneralCli().HMGet(ctx, key, playerIds...).Result()
	if err != nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, fmt.Sprintf("rankPlayer[%+v:%+v] fail:%+v", rankId, round, err))
	}
	// HMGet 查询失败 bean 对应位置返回nil，无err

	list := make([]*model.TRankPlayer, 0)
	for i, bean := range beans {
		if str, ok := bean.(string); ok {
			beanData := []byte(str)
			list = append(list, &model.TRankPlayer{})
			err := list[i].Deserialize(beanData)
			if err != nil {
				return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, fmt.Sprintf("rankPlayer[%+v:%+v:%+v] deserialze fail:%+v", rankId, round, playerIds[i], err))
			}
		}
	}

	return list, nil
}

func UpdatePlayerInfo(ctx context.Context, sch *model.TRankSchedule, playerInfo *model.TRankPlayer) error {
	key := config.GetRedisKeyRankPlayer(sch.RankId, sch.Round)

	expire := sch.GetExpire()
	pipe := redisx.GetGeneralCli().Pipeline()
	pipe.HSet(ctx, key, playerInfo.PlayerId, playerInfo.Serialize())
	pipe.ExpireAt(ctx, key, utility.RandTimeExpire(expire))
	_, err := pipe.Exec(ctx)
	if err != nil {
		// TODO: protox
		return err
	}

	return nil
}
