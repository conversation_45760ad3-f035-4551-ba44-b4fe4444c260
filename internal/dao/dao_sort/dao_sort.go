package dao_sort

import (
	"context"
	"errors"
	"fmt"
	"ranksrv/config"
	"ranksrv/internal/model"
	"ranksrv/utility"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// FindRankSortRange 获取排行榜排序
func FindRankSortRange(ctx context.Context, rankId int64, round int32, start, end int32) ([]string, error) {
	var err error
	// start min = 0
	// end = -1 全选
	key := config.GetRedisKeyRankSort(rankId, round)
	playerIds, err := redisx.GetGeneralCli().ZRevRange(ctx, key, int64(start), int64(end)).Result()
	if err != nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, fmt.Sprintf("ranksort[%+v:%+v] fail:%+v", rankId, round, err))
	}

	return playerIds, nil
}

// FindRankSortPlayer 针对用户查询排行榜
func FindRankSortPlayer(ctx context.Context, rankId int64, round int32, playerId uint64) (int64, error) {
	key := config.GetRedisKeyRankSort(rankId, round)
	sort, err := redisx.GetGeneralCli().ZRank(ctx, key, fmt.Sprintf("%d", playerId)).Result()
	if err != nil {
		// 空表的时候
		if errors.Is(err, redis.Nil) {
			return config.DEFAULT_RANK_UNRANK, nil
		}
		return config.DEFAULT_RANK_UNRANK, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, fmt.Sprintf("ranksort[%+v:%+v:%+v] fail:%+v", rankId, round, playerId, err))
	}
	return sort, nil
}

// 更新排行权重
func UpdateRankSort(ctx context.Context, sch *model.TRankSchedule, playerId uint64, weight int64) error {
	key := config.GetRedisKeyRankSort(sch.RankId, sch.Round)
	pipe := redisx.GetGeneralCli().Pipeline()
	pipe.ZAdd(ctx, key, &redis.Z{Score: float64(weight), Member: fmt.Sprintf("%d", playerId)})
	pipe.ExpireAt(ctx, key, utility.RandTimeExpire(sch.GetExpire()))
	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}
