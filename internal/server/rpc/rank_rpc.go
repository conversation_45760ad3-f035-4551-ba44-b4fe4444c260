package rpc

import (
	"context"
	"ranksrv/internal/services"

	rankRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/rankrpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc"
)

type RankRpcService struct {
}

// 提交结算数据
func (*RankRpcService) SubmitTripSettle(ctx context.Context, req *rankRpc.SubmitTripSettleReq) (*rankRpc.SubmitTipSettleRsp, error) {
	return services.GetRankServiceInstance().SubmitTripSettle(ctx, req)
}

func InitRankRpcService() {
	rpcService := &RankRpcService{}
	rankRpc.RegisterRankServiceServer(rpc.Server, rpcService)
}
