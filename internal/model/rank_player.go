package model

import (
	"encoding/json"
	"ranksrv/config"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// 用户数据
type TRankPlayer struct {
	PlayerId     uint64                           `json:"player_id"`
	Sort         int32                            `json:"-"`    // 实时数据不记录
	Data         map[commonPB.RANK_INFO_KEY]int64 `json:"data"` // 缓存数据
	UpdateTs     int64                            `json:"update_ts"`
	RewardStatus config.RANK_REWARD_STATUS        `json:"reward_status"` // 奖励状态
	RewardTs     int64                            `json:"reward_ts"`     // 奖励时间
}

func NewPlayer(playerId uint64) *TRankPlayer {
	return &TRankPlayer{
		PlayerId: playerId,
		Data:     make(map[commonPB.RANK_INFO_KEY]int64),
	}
}

func (TRankPlayer) TableName() string {
	return "rank_player"
}

func (t *TRankPlayer) Serialize() []byte {
	jsStr, _ := json.Marshal(t)

	return jsStr
}

func (t *TRankPlayer) Deserialize(str []byte) error {
	err := json.Unmarshal(str, t)
	return err
}
