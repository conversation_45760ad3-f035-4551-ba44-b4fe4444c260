package model

import (
	"encoding/json"
	"ranksrv/config"
	"ranksrv/utility"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
)

// 排行榜排期信息
type TRankSchedule struct {
	RankId       int64              `json:"rank_id"`     // 排行榜id
	Round        int32              `json:"round"`       // 排行榜周期
	RankType     commonPB.RANK_TYPE `json:"rank_type"`   // 排行类型
	Status       config.RANK_STATUS `json:"status"`      // 状态
	FlushTime    int64              `json:"flush_time"`  // 刷新时间
	RewardTime   int64              `json:"reward_time"` // 发奖时间
	RewardStatus int                `json:"reward_status"`
}

func (t *TRankSchedule) GetExpire() time.Time {
	var endTs time.Time
	if t.FlushTime != 0 {
		// 需要判断是否已结束
		endTs = time.Unix(t.FlushTime, 0).Add(config.RediskeyDefaultExpire)
	} else {
		// // 按照发奖时间设置过期时间
		if t.RewardTime != 0 {
			endTs = time.Unix(t.RewardTime, 0).Add(2 * config.RediskeyDefaultExpire)
		} else {
			// 设置默认过期时间
			endTs = timex.Now().Add(config.RediskeyPresentxpire)
		}
	}
	return endTs
}

func (t *TRankSchedule) Deserialize(data []byte) error {
	err := json.Unmarshal(data, &t)
	if err != nil {
		return err
	}
	return nil
}

func (t *TRankSchedule) Serialize() []byte {
	jsStr, err := json.Marshal(t)
	if err != nil {
		logrus.Errorf("TRankSchedule Json Unmarshral:%+v str:%+v", err, jsStr)
		return nil
	}

	return jsStr
}

// NewSchFromConfig 生成Schedule
func NewSchFromConfig(cfg *cmodel.Rank) *TRankSchedule {
	sch := &TRankSchedule{
		RankId:   cfg.Id,
		Round:    0,
		RankType: commonPB.RANK_TYPE(cfg.Rule),
		Status:   config.RANK_STATUS_OPEN,
	}
	t := timex.Now()
	// 初始化时间
	sch.FlushTime = utility.NextFlushTs(commonPB.FLUSH_TIME_TYPE(cfg.FlushType), t, int64(cfg.FlushTime))
	sch.RewardTime = utility.NextFlushTs(commonPB.FLUSH_TIME_TYPE(cfg.RewardType), t, int64(cfg.RewardTime))

	return sch
}
