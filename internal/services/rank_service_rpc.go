package services

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	rankRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/rankrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

func (s *RankService) SubmitTripSettle(ctx context.Context, req *rankRpc.SubmitTripSettleReq) (*rankRpc.SubmitTipSettleRsp, error) {
	rsp := &rankRpc.SubmitTipSettleRsp{
		Ret: protox.DefaultResult(),
	}
	// 解析中鱼结果

	// code review [chenlinghao]: req中没有赋值WeightFish和ValFish的时候该怎么处理 packFishInfo 函数中也没有判断参数是否有效

	opt := interceptor.GetRPCOptions(ctx)
	weightFish := req.WeightFish
	valFish := req.ValFish
	if weightFish != nil {
		// 最重的鱼
		weightFishEvent := &commonPB.EventCommon{
			PlayerId:  req.PlayerId,
			ProductId: opt.ProductId,
			ChannelId: opt.ChannelType,
			EventType: commonPB.EVENT_TYPE_ET_TRIP_SETTLE_WEIGHT,
			IntData:   packFishInfo(weightFish, req.PondId),
		}
		s.HandleEventUpdate(ctx, req.PlayerId, weightFishEvent)
	}

	if valFish != nil {
		// 最有价值的鱼
		valFishEvent := &commonPB.EventCommon{
			PlayerId:  req.PlayerId,
			ProductId: opt.ProductId,
			ChannelId: opt.ChannelType,
			EventType: commonPB.EVENT_TYPE_ET_TRIP_SETTLE_VAL,
			IntData:   packFishInfo(valFish, req.PondId),
		}
		s.HandleEventUpdate(ctx, req.PlayerId, valFishEvent)
	}

	return rsp, nil
}

func packFishInfo(fish *commonPB.FishDetailInfo, pondId int64) map[int32]int64 {
	return map[int32]int64{
		int32(commonPB.EVENT_INT_KEY_EIK_FISH_POND):     pondId,
		int32(commonPB.EVENT_INT_KEY_EIK_FISH_BAIT):     fish.GetBaitId(),
		int32(commonPB.EVENT_INT_KEY_EIK_FISH_ID):       fish.GetFishInfo().GetFishId(),
		int32(commonPB.EVENT_INT_KEY_EIK_FISH_LENGTH):   int64(fish.GetFishInfo().GetLength()),
		int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT):   int64(fish.GetFishInfo().GetWeight()),
		int32(commonPB.EVENT_INT_KEY_EIK_TRIP_FISH_VAL): int64(fish.GetFishInfo().GetAwardNum()),
		int32(commonPB.EVENT_INT_KEY_EIK_FISH_BROKEN):   int64(fish.GetFishDamagedInfo().GetFishDamagedLv()),
	}
}
