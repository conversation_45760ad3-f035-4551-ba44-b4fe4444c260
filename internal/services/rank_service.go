package services

import (
	"context"
	logic_rank "ranksrv/internal/logic/rank"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	rankPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/rank"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

type RankService struct {
	Ctx  context.Context
	Name string
}

var (
	rankServiceInstance *RankService
	once                sync.Once
)

// GetRankService 返回 RankService 的单例实例
func GetRankServiceInstance() *RankService {
	once.Do(func() {
		rankServiceInstance = &RankService{}
		rankServiceInstance.Init()
	})
	return rankServiceInstance
}

// Init 初始化 RankService
func (s *RankService) Init() {

}

// GetRankListReq 查询排行榜
func (s *RankService) GetRankListReq(ctx context.Context, req *rankPB.GetRankListReq) *rankPB.GetRankListRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &rankPB.GetRankListRsp{Ret: protox.DefaultResult()}
	entry.Debugf("GetRankListReq: %+v", req)
	rsp.Id = req.Id

	sch, err := logic_rank.GetRankSchedule(ctx, req.Id)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}
	rsp.Round = sch.Round

	list, err := logic_rank.GetRankSort(ctx, req.Id, sch.Round, req.Start-1, req.End-1)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	// 返回数据
	rsp.List = logic_rank.MakePBRankPlayer(ctx, list...)
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	// 查询自己排名
	opt := interceptor.GetRPCOptions(ctx)
	inRankflag := false
	for _, p := range rsp.List {
		if p.User.PlayerId == opt.PlayerId {
			rsp.Self = p
			inRankflag = true
			break
		}
	}
	if !inRankflag {
		selfInfo, err := logic_rank.GetPlayerRank(ctx, sch.RankId, sch.Round, opt.PlayerId)
		if err != nil {
			entry.Errorf("get player info fail:%+v", err)
			return rsp
		}
		l := logic_rank.MakePBRankPlayer(ctx, selfInfo)
		rsp.Self = l[0]
	}

	return rsp
}

// 监听事件更新
func (s *RankService) HandleEventUpdate(ctx context.Context, playerId uint64, event *commonPB.EventCommon) {
	entry := logx.NewLogEntry(ctx)
	_ = entry

	// 用户数据更新锁
	logic_rank.UpdateRankPlayer(ctx, event)
	// 过滤有效性

	return
}

// 手动领取奖励
