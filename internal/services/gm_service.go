package services

import (
	"context"
	"fmt"
	logic_rank "ranksrv/internal/logic/rank"
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/gm_handler"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"github.com/sirupsen/logrus"
)

type RankGmService struct {
	Ctx  context.Context
	Name string
}

var (
	rankGmServiceInstance *RankGmService
	onceGm                sync.Once
)

// GetRankGmServiceInstance 返回 RankGmService 的单例实例
func GetRankGmServiceInstance() *RankGmService {
	onceGm.Do(func() {
		rankGmServiceInstance = &RankGmService{}
		rankGmServiceInstance.Init()
	})
	return rankGmServiceInstance
}

// Init 初始化 RankGmService
func (s *RankGmService) Init() {
	// 可在此处添加 GM 服务特有的初始化逻辑
	// 例如：s.Name = "RankGMService"
}

func (s *RankGmService) FlushRank(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("[gm:flush]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}

	reqd := &gmPB.GmCmdFlushRankReq{}
	err := gm_handler.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		entry.Errorf("[gm:flush]:%s", err.Error())
		return rsp, err
	}

	rankCfg := cmodel.GetRank(reqd.RankId)
	if rankCfg == nil {
		entry.Errorf("[gm:flush]:rank not found:%d", reqd.RankId)
		return rsp, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("rank not found:%d", reqd.RankId))
	}

	sch, err := logic_rank.GetRankSchedule(ctx, reqd.RankId)
	if err != nil {
		entry.Errorf("[gm:flush]:%s", err.Error())
		return rsp, err
	}
	logic_rank.InitSch(ctx, rankCfg, sch.Round+1)

	logrus.Infof("[gm:flush]:success")
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS, "success")
	return rsp, nil
}

func (s *RankGmService) RewardRank(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("[gm:reward]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}

	reqd := &gmPB.GmCmdRewardRankReq{}
	err := gm_handler.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		entry.Errorf("[gm:reward]:%s", err.Error())
		return rsp, err
	}

	sch, err := logic_rank.GetRankSchedule(ctx, reqd.RankId)
	if err != nil {
		entry.Errorf("[gm:reward]:%s", err.Error())
		return rsp, err
	}
	logic_rank.RewardSch(ctx, sch, true)

	logrus.Infof("[gm:reward]:success")
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS, "success")
	return rsp, nil
}
