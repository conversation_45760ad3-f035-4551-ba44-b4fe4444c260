package rpc_msg

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	msgrpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/msgrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_msg"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func SendMail(ctx context.Context, playerId uint64, mail *commonPB.MailAssembly) error {
	cli := crpc_msg.GetMsgRpcInstance().GetMsgRpcClient()
	if cli == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "rpc fail")
	}

	req := &msgrpc.SendMailReq{
		Sender:    viper.GetString(dict.ConfigRpcServerName),
		PlayerIds: []uint64{playerId},
		ProductId: commonPB.PRODUCT_ID_PID_FISHER,
		Channel:   commonPB.CHANNEL_TYPE_CT_GOOGLE,
		Assembly:  mail,
	}

	hRet, errRet := cli.SendMail(ctx, req)
	if errRet != nil {
		return errRet
	}
	_ = hRet
	return nil
}
