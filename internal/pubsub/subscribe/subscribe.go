package pubsub

import (
	"ranksrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/eventx"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

func InitSub() {
	// serverName := viper.GetString(dict.ConfigRpcServerName)


}

func HandleEvent(body []byte) {
	event := &commonPB.EventCommon{}
	if err := proto.Unmarshal(body, event); err != nil {
		logrus.Errorf("handler event unmarshal error: %v", err)
		return
	}
	logrus.Debugf("HandleEvent: %v", event)
	ctx := eventx.EventPlayerCtx(event)
	_ = ctx

	services.GetRankServiceInstance().HandleEventUpdate(ctx, event.PlayerId, event)
}
