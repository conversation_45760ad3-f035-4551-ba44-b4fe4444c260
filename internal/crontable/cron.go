package crontable

import (
	"ranksrv/config"
	logic_rank "ranksrv/internal/logic/rank"

	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"github.com/robfig/cron"
)

type CTable struct {
}

func Init() {
	c := cron.New()
	// 每天5点执行
	c.AddFunc("0 0 5 * * ?", Do)

	// 启动定时任务
	c.Start()
	// 启动服务器的时候检查初始化榜单
	Do()
}

func Do() {
	// 分布式锁
	// 保证单次任务执行抢锁
	IsGet, unlock := dlm.DefaultLockMgr.OptimisticLockKey(config.RedisLockRankCron, 3600)
	if !IsGet {
		return
	}
	defer unlock()

	// TODO 重写时间表
	logic_rank.CheckSchedule()
}
