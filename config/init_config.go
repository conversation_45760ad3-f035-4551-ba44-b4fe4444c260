package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

func InitConfig() error {
	serviceConfig := config.NewServiceConfig()

	serviceConfig.Register("InitRankCfg", cmodel.InitRankCfg)             // 排行榜配置
	serviceConfig.Register("InitRankRewardCfg", cmodel.InitRankRewardCfg) // 排行榜奖励配置
	serviceConfig.Register("InitItemCfg", cmodel.InitItemCfg)             // 道具配置表

	return serviceConfig.ExecuteAll()
}
