package config

import (
	"fmt"
	"time"
)

const (
	RediskeyDefaultExpire = 3 * 86400 * time.Second
	RediskeyPresentxpire  = 7 * 86400 * time.Second
)

const (
	RedisLockRankCron = "lock:rank:cron"
	// 排行用户数据锁 "lock:rank:player:{playerid}"
	RedisLockRankPlayer = "lock:rank:player:%d"
)

// code review [chenlinghao]: 使用rank 做为排行榜的标记key r无法辨识

const (
	// [Hset]排行用户数据 r:player:{rankid}:{round}
	RedisKeyRankPlayer = "r:p:%d:%d"
	// [Zset] 排行数据 r:sort:{rankid}:{round}
	RedisKeyRankSort = "r:s:%d:%d"
	// [Set]排行榜排期数据 r:Schedule:{rankid}
	RedisKeyRankSchedule = "r:sch:%d"
	// [Hset] 排行榜列表数据
	RedisKeyRankScheduleList = "r:sch:list"
)

// GetRedisKeyRankSchdule 获取排行榜排期数据Key
func GetRedisKeyRankSchdule(rankId int64) string {
	return fmt.Sprintf(RedisKeyRankSchedule, rankId)
}

// GetRedisKeyRankPlayer 获取排行榜用户信息key
func GetRedisKeyRankPlayer(rankId int64, round int32) string {
	return fmt.Sprintf(RedisKeyRankPlayer, rankId, round)
}

// GetRedisKeyRankSort 获取排行榜数据key
func GetRedisKeyRankSort(rankId int64, round int32) string {
	return fmt.Sprintf(RedisKeyRankSort, rankId, round)
}

// GetRedisLockRankPlayer 获取排行榜用户信息锁key
func GetRedisLockRankPlayer(playerId int64) string {
	return fmt.Sprintf(RedisLockRankPlayer, playerId)
}

// GetRedisKeyRankSchduleList 获取排行榜列表key
func GetRedisKeyRankSchduleList() string {
	return RedisKeyRankScheduleList
}
