package main

import (
	"context"
	"ranksrv/config"
	"ranksrv/internal/crontable"
	"ranksrv/internal/proc"
	"ranksrv/internal/server/rpc"
	"runtime"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type rankService struct {
	Name string
	Ctx  context.Context
}

func (s *rankService) Init() error {
	s.Ctx = context.Background()
	s.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infoln(s.Name + "服务Init")

	// TODO 实现各模块初始化操作
	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}

	proc.RegClientMsgHandler()
	rpc.InitRankRpcService()

	return nil
}

func (s *rankService) Start() error {
	// TODO 这里实现启动逻辑
	logrus.Infoln(s.Name + "服务启动成功")

	crontable.Init()

	return nil
}

func (s *rankService) Stop() error {
	// TODO 这里实现服务正常关闭逻辑
	logrus.Infoln(s.Name + "服务关闭中...")
	return nil
}

func (s *rankService) ForceStop() error {
	// TODO 这里实现强制关闭逻辑
	logrus.Infoln(s.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource() // 初始化随机数
	driver.Run(&rankService{})
}
