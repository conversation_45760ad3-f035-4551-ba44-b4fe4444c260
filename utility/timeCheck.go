package utility

import (
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
)

func NextFlushTs(ft commonPB.FLUSH_TIME_TYPE, t time.Time, target int64) int64 {
	tx := timex.NewWorkTimeModel(timex.TIME_MODEL_OPERATE)
	

	// tx := timex.NewOperateTime(t)
	switch ft {
	case commonPB.FLUSH_TIME_TYPE_FTT_PERMANENT:
		return 0
	case commonPB.FLUSH_TIME_TYPE_FTT_DAY:
		return tx.NextDay(t, 1).Unix()
	case commonPB.FLUSH_TIME_TYPE_FTT_WEEK:
		nextWeek, err := tx.NextWeek(t, time.Weekday(target), 1)
		if err != nil {
			logrus.Errorf("NextWeek error:%+v", err)
			return 0
		}
		return nextWeek.Unix()
	case commonPB.FLUSH_TIME_TYPE_FTT_MONTH:
		nextMonth, err := tx.NextMonth(t, int(target), 1)
		if err != nil {
			logrus.Errorf("NextMonth error:%+v", err)
			return 0
		}
		return nextMonth.Unix()
	default:
		logrus.Errorf("FlushType:%d not support target:%d", ft, target)
		return 0
	}
}

// 获取计算 反码时间 2^29
func TimeVal(ts int64) int64 {
	// 1735660800 = 2025/1/1
	// 大约记录5.6年

	return 2<<29 - (ts - 1735660800)
}

func RandTimeExpire(ts time.Time) time.Time {
	rdtime := random.Int64n(5 * 60)
	return ts.Add(time.Duration(rdtime) * time.Second)
}
