package test

import (
	"context"
	"fmt"
	"path"
	"ranksrv/config"
	"runtime"
	"strings"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func Init() {
	// severHost := "localhost"
	severHost := "************"
	InitLog()
	InitRedis(severHost)
	InitSql(severHost)
	InitConsul(severHost)
	config.InitConfig()
}

// TestCtx 获取测试用context
func TestCtx(playerId uint64) context.Context {
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithPlayerId(playerId),
		interceptor.WithProductId(1),
		interceptor.WithChannelType(1),
	)
	return ctx
}

func InitRedis(server string) {
	addr := server + ":6379"
	passwd := "8888"
	conf := map[string]string{
		"addr":   server + ":6379",
		"passwd": "8888",
	}
	viper.Set(dict.ConfigRedisAddr, addr)
	viper.Set(dict.ConfigRedisPwd, passwd)
	viper.Set("redis_list", map[string]interface{}{
		dict_redis.RDBGeneral: conf,
	})
}

func InitConsul(server string) {
	viper.SetDefault(dict.ConfigConsulAddr, server+":8500")
}

func InitLog() {
	logrus.SetLevel(logrus.DebugLevel)
	logrus.SetReportCaller(true)
	logrus.SetFormatter(&logrus.JSONFormatter{
		DisableTimestamp: false,
		// TimestampFormat:  "2006-01-02 15:04:05",
		TimestampFormat: time.RFC3339Nano,
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			s := strings.Split(f.Function, ".")
			funcName := s[len(s)-1]
			filepath, filename := path.Split(f.File)
			return funcName, filepath + filename + fmt.Sprintf(":%d", f.Line)
		},
		PrettyPrint: false,
	})
}

func InitSql(server string) {
	conf := map[string]interface{}{
		"addr":   server + ":3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     dict_mysql.MysqlDBTask,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBTask: conf,
	})
}

func InitNsq(server string) {
	viper.SetDefault(dict.ConfigNsqDAddr, server+":4150")
	viper.SetDefault(dict.ConfigNsqHttpAddr, server+":4151")
	viper.SetDefault(dict.ConfigNsqLookUpdAddress, server+":4161")
	nsqx.Setup()
}
